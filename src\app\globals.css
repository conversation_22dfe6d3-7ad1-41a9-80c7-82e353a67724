@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif;
  --font-mono: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas,
    "Courier New", monospace;
  --font-hertical-sans: var(--font-hertical-sans);
  --font-amnestia: var(--font-amnestia);
  --font-impact-regular: var(--font-impact-regular);
  --font-gothic-cg: var(--font-gothic-cg);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* 3D Coin rotation animation - horizontal only */
@keyframes spin-3d {
  0% {
    transform: rotateY(0deg);
  }
  100% {
    transform: rotateY(360deg);
  }
}

.animate-spin-3d {
  animation: spin-3d 4s linear infinite;
  transform-style: preserve-3d;
}

.coin-container {
  perspective: 1000px;
  perspective-origin: center center;
}

/* Floating animation for decorative elements */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Pulse glow animation */
@keyframes pulse-glow {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(211, 177, 54, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(211, 177, 54, 0.6),
      0 0 40px rgba(211, 177, 54, 0.3);
  }
}

/* Teal glow animation for active elements */
@keyframes pulse-glow-teal {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(0, 188, 212, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(0, 188, 212, 0.6), 0 0 40px rgba(0, 188, 212, 0.3);
  }
}

.animate-pulse-glow-teal {
  animation: pulse-glow-teal 2s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Shimmer effect for borders */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.border-shimmer {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(211, 177, 54, 0.4),
    transparent
  );
  background-size: 200% 100%;
  animation: shimmer 3s linear infinite;
}

/* Subtle background pattern */
.bg-pattern {
  background-image: radial-gradient(
      circle at 25% 25%,
      rgba(211, 177, 54, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 75% 75%,
      rgba(147, 51, 234, 0.1) 0%,
      transparent 50%
    );
}

/* Enhanced button hover effects */
.btn-enhanced {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.btn-enhanced::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.btn-enhanced:hover::before {
  left: 100%;
}

/* Slide in from right animation */
@keyframes slideInRight {
  0% {
    transform: translateX(50px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

.animate-slide-in-right {
  animation: slideInRight 0.7s ease-out;
}

/* Fade in up animation */
@keyframes fadeInUp {
  0% {
    transform: translateY(30px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out;
}

/* Golden border glow animation */
@keyframes goldenGlow {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(211, 177, 54, 0.4),
      inset 0 0 20px rgba(211, 177, 54, 0.1);
  }
  50% {
    box-shadow: 0 0 40px rgba(211, 177, 54, 0.8),
      inset 0 0 30px rgba(211, 177, 54, 0.2);
  }
}

.animate-golden-glow {
  animation: goldenGlow 3s ease-in-out infinite;
}

/* Roadmap specific animations */
@keyframes roadmapSlideIn {
  0% {
    transform: translateX(50px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

.animate-roadmap-slide {
  animation: roadmapSlideIn 0.6s ease-out;
}

/* Timeline pulse animation */
@keyframes timelinePulse {
  0%,
  100% {
    box-shadow: 0 0 0 0 rgba(211, 177, 54, 0.7);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(211, 177, 54, 0);
  }
}

.animate-timeline-pulse {
  animation: timelinePulse 2s infinite;
}

/* Milestone completion animation */
@keyframes milestoneComplete {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

.animate-milestone-complete {
  animation: milestoneComplete 0.6s ease-in-out;
}

/* Whitepaper specific animations */
@keyframes whitepaperFadeIn {
  0% {
    transform: translateY(30px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-whitepaper-fade {
  animation: whitepaperFadeIn 0.7s ease-out;
}

/* Section hover scale */
.scale-102 {
  transform: scale(1.02);
}

/* Card hover effects */
.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

/* Text glow effect */
.text-glow {
  text-shadow: 0 0 10px rgba(211, 177, 54, 0.5);
}

/* Gradient text effect */
.text-gradient {
  background: linear-gradient(45deg, #d3b136, #fbbf24, #d3b136);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradient-shift 3s ease infinite;
}

@keyframes gradient-shift {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Particle effect background */
.particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.particle {
  position: absolute;
  width: 2px;
  height: 2px;
  background: rgba(211, 177, 54, 0.6);
  border-radius: 50%;
  animation: particle-float 6s linear infinite;
}

@keyframes particle-float {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-10vh) rotate(360deg);
    opacity: 0;
  }
}

/* Enhanced border effects */
.border-magic {
  position: relative;
  overflow: hidden;
}

.border-magic::before {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #d3b136, #9333ea, #d3b136, #9333ea);
  background-size: 400% 400%;
  animation: gradient-shift 4s ease infinite;
  z-index: -1;
  border-radius: inherit;
}

/* Mystical glow for important elements */
.mystical-glow {
  position: relative;
}

.mystical-glow::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    circle,
    rgba(211, 177, 54, 0.3) 0%,
    transparent 70%
  );
  transform: translate(-50%, -50%);
  animation: mystical-pulse 3s ease-in-out infinite;
  pointer-events: none;
  z-index: -1;
}

@keyframes mystical-pulse {
  0%,
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.3;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.6;
  }
}
